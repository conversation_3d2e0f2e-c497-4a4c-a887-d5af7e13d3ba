use rdev::{listen, Event, EventType};
use serde::Serialize;
use serde_json::json;

#[cfg(target_os = "macos")]
use cocoa::appkit::NSApplication;
#[cfg(target_os = "macos")]
use cocoa::base::{id, nil};
#[cfg(target_os = "macos")]
use cocoa::foundation::NSString;
#[cfg(target_os = "macos")]
use objc::{msg_send, sel, sel_impl, runtime};

#[cfg(target_os = "windows")]
use winapi::um::winuser::{GetForegroundWindow, SetForegroundWindow, GetWindowThreadProcessId};
#[cfg(target_os = "windows")]
use winapi::um::processthreadsapi::GetCurrentProcessId;

#[derive(Serialize)]
struct RdevEvent {
    event_type: String,
    name: Option<String>,
    time: std::time::SystemTime,
    data: String,
}



fn deal_event_to_json(event: Event) -> RdevEvent {
    let mut jsonify_event = RdevEvent {
        event_type: "".to_string(),
        name: event.name,
        time: event.time,
        data: "".to_string(),
    };
    match event.event_type {
        EventType::KeyPress(key) => {
            jsonify_event.event_type = "KeyPress".to_string();
            jsonify_event.data = json!({
                "key": format!("{:?}", key)
            })
            .to_string();
        }
        EventType::KeyRelease(key) => {
            jsonify_event.event_type = "KeyRelease".to_string();
            jsonify_event.data = json!({
                "key": format!("{:?}", key)
            })
            .to_string();
        }
        EventType::MouseMove { x, y } => {
            jsonify_event.event_type = "MouseMove".to_string();
            jsonify_event.data = json!({
                "x": x,
                "y": y
            })
            .to_string();
        }
        EventType::ButtonPress(key) => {
            jsonify_event.event_type = "ButtonPress".to_string();
            jsonify_event.data = json!({
                "key": format!("{:?}", key)
            })
            .to_string();
        }
        EventType::ButtonRelease(key) => {
            jsonify_event.event_type = "ButtonRelease".to_string();
            jsonify_event.data = json!({
                "key": format!("{:?}", key)
            })
            .to_string();
        }
        EventType::Wheel { delta_x, delta_y } => {
            jsonify_event.event_type = "Wheel".to_string();
            jsonify_event.data = json!({
                "delta_x": delta_x,
                "delta_y": delta_y
            })
            .to_string();
        }
    }

    jsonify_event
}

fn write_text(text: &str) -> Result<(), Box<dyn std::error::Error>> {
    use enigo::{Enigo, Keyboard, Settings};

    let mut enigo = match Enigo::new(&Settings::default()) {
        Ok(enigo) => enigo,
        Err(e) => {
            eprintln!("Failed to create Enigo instance: {}", e);
            return Err(Box::new(e));
        }
    };

    match enigo.text(text) {
        Ok(_) => Ok(()),
        Err(e) => {
            eprintln!("Failed to write text: {}", e);
            Err(Box::new(e))
        }
    }
}

#[cfg(target_os = "macos")]
fn get_focused_app_info() -> Result<String, Box<dyn std::error::Error>> {
    unsafe {
        // Get the shared workspace
        let workspace_class = objc::runtime::objc_getClass("NSWorkspace\0".as_ptr() as *const i8);
        let workspace: id = msg_send![workspace_class, sharedWorkspace];

        // Get the frontmost application
        let frontmost_app: id = msg_send![workspace, frontmostApplication];

        if frontmost_app == nil {
            return Err("No frontmost application found".into());
        }

        // Get bundle identifier and process ID
        let bundle_id: id = msg_send![frontmost_app, bundleIdentifier];
        let process_id: i32 = msg_send![frontmost_app, processIdentifier];

        if bundle_id == nil {
            return Err("Could not get bundle identifier".into());
        }

        // Convert NSString to Rust string
        let bundle_id_ptr: *const i8 = msg_send![bundle_id, UTF8String];
        let bundle_id_string = std::ffi::CStr::from_ptr(bundle_id_ptr).to_string_lossy();

        Ok(format!("{}:{}", bundle_id_string, process_id))
    }
}

#[cfg(target_os = "windows")]
fn get_focused_app_info() -> Result<String, Box<dyn std::error::Error>> {
    unsafe {
        let hwnd = GetForegroundWindow();
        if hwnd.is_null() {
            return Err("No foreground window found".into());
        }

        let mut process_id: u32 = 0;
        GetWindowThreadProcessId(hwnd, &mut process_id);

        Ok(format!("hwnd:{}:pid:{}", hwnd as usize, process_id))
    }
}

#[cfg(not(any(target_os = "macos", target_os = "windows")))]
fn get_focused_app_info() -> Result<String, Box<dyn std::error::Error>> {
    Err("Focus management not implemented for this platform".into())
}

#[cfg(target_os = "macos")]
fn restore_focus_to_app(app_info: &str) -> Result<(), Box<dyn std::error::Error>> {
    let parts: Vec<&str> = app_info.split(':').collect();
    if parts.len() != 2 {
        return Err("Invalid app info format".into());
    }

    let _bundle_id = parts[0];
    let process_id: i32 = parts[1].parse().map_err(|_| "Invalid process ID")?;

    unsafe {
        // Get the shared workspace
        let workspace_class = objc::runtime::objc_getClass("NSWorkspace\0".as_ptr() as *const i8);
        let workspace: id = msg_send![workspace_class, sharedWorkspace];

        // Get running applications
        let running_apps: id = msg_send![workspace, runningApplications];
        let count: usize = msg_send![running_apps, count];

        for i in 0..count {
            let app: id = msg_send![running_apps, objectAtIndex: i];
            let app_process_id: i32 = msg_send![app, processIdentifier];

            if app_process_id == process_id {
                // Activate the application
                let _: () = msg_send![app, activateWithOptions: 0];
                return Ok(());
            }
        }

        Err("Could not find application to restore focus".into())
    }
}

#[cfg(target_os = "windows")]
fn restore_focus_to_app(app_info: &str) -> Result<(), Box<dyn std::error::Error>> {
    let parts: Vec<&str> = app_info.split(':').collect();
    if parts.len() != 4 || parts[0] != "hwnd" || parts[2] != "pid" {
        return Err("Invalid app info format".into());
    }

    let hwnd: usize = parts[1].parse().map_err(|_| "Invalid window handle")?;

    unsafe {
        let result = SetForegroundWindow(hwnd as *mut _);
        if result == 0 {
            return Err("Failed to set foreground window".into());
        }
    }

    Ok(())
}

#[cfg(not(any(target_os = "macos", target_os = "windows")))]
fn restore_focus_to_app(_app_info: &str) -> Result<(), Box<dyn std::error::Error>> {
    Err("Focus management not implemented for this platform".into())
}

fn main() {
    let args: Vec<String> = std::env::args().collect();

    if args.len() > 1 && args[1] == "listen" {
        if let Err(error) = listen(move |event| match event.event_type {
            EventType::KeyPress(_) | EventType::KeyRelease(_) => {
                let event = deal_event_to_json(event);
                println!("{}", serde_json::to_string(&event).unwrap());
            }

            _ => {}
        }) {
            eprintln!("!error: {:?}", error);
            std::process::exit(1);
        }
    } else if args.len() > 2 && args[1] == "write" {
        let text = args[2].clone();

        match write_text(text.as_str()) {
            Ok(_) => {
                std::process::exit(0);
            },
            Err(e) => {
                eprintln!("Write command failed: {}", e);
                std::process::exit(101);
            }
        }
    } else if args.len() > 1 && args[1] == "get-focus" {
        match get_focused_app_info() {
            Ok(app_info) => {
                println!("{}", app_info);
                std::process::exit(0);
            },
            Err(e) => {
                eprintln!("Get focus command failed: {}", e);
                std::process::exit(102);
            }
        }
    } else if args.len() > 2 && args[1] == "restore-focus" {
        let app_info = args[2].clone();

        match restore_focus_to_app(&app_info) {
            Ok(_) => {
                std::process::exit(0);
            },
            Err(e) => {
                eprintln!("Restore focus command failed: {}", e);
                std::process::exit(103);
            }
        }
    } else {
        eprintln!("Usage: {} [listen|write <text>|get-focus|restore-focus <app_info>]", args.get(0).unwrap_or(&"speakmcp-rs".to_string()));
        eprintln!("Commands:");
        eprintln!("  listen                    - Listen for keyboard events");
        eprintln!("  write <text>              - Write text using accessibility API");
        eprintln!("  get-focus                 - Get currently focused application info");
        eprintln!("  restore-focus <app_info>  - Restore focus to specified application");
        std::process::exit(1);
    }
}
