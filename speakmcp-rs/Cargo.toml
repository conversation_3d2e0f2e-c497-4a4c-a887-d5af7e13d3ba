[package]
name = "speakmcp-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
rdev = "0.5.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
enigo = "0.3.0"

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.25"
objc = "0.2"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi", "handleapi"] }

[profile.release]
strip = true
